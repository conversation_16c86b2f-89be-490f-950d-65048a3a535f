import type { 
    SubscriptionQuotaData, 
    SubscriptionInfoData, 
    SubscriptionTierData 
} from '$lib/api/types/subscription-api';
import type { 
    SubscriptionData, 
    CurrentSubscription, 
    UsageStats, 
    SubscriptionTier,
    FeatureCategory 
} from '$lib/types/subscription';

/**
 * Transform backend API response to frontend component format
 */
export class SubscriptionTransformer {
    
    /**
     * Transform quota status data to current subscription format
     */
    static transformToCurrentSubscription(quotaData: SubscriptionQuotaData, infoData?: SubscriptionInfoData): CurrentSubscription {
        return {
            tier: quotaData.tier_name,
            status: quotaData.status,
            expiresAt: quotaData.expires_at,
            serialNumber: infoData?.subscription_key || 'N/A',
            organizationName: quotaData.organization_name,
            activatedOn: infoData?.activated_on
        };
    }

    /**
     * Transform quota data to usage statistics format
     */
    static transformToUsageStats(quotaData: SubscriptionQuotaData): UsageStats {
        return {
            currentUsers: quotaData.quota.current_active_users,
            storageUsed: `${Math.round((quotaData.quota.current_active_users / quotaData.quota.max_active_users) * 100)}%`, // Placeholder calculation
            messagesThisMonth: 0, // This would need to come from another API endpoint
            workflowsActive: 0, // This would need to come from another API endpoint
            maxUsers: quotaData.quota.max_active_users,
            remainingSlots: quotaData.quota.remaining_slots,
            maxStorage: quotaData.quota.max_storage_gb,
            maxMessagesPerMin: quotaData.quota.max_messages_per_min
        };
    }

    /**
     * Transform backend tier data to frontend tier format
     */
    static transformToSubscriptionTier(tierData: SubscriptionTierData): SubscriptionTier {
        // Map backend feature names to frontend feature names
        const featureMap = {
            'Active User Accounts': tierData.quota.max_active_users.toString(),
            'Transfer Algorithm': tierData.features.custom_transfer_algo ? 'Customization' : 'Round-Robin',
            'Case Description': tierData.features.custom_case_desc ? 'Customization' : 'Predefined',
            'Quick Reply': tierData.features.ai_quick_reply,
            'Smart Reply': tierData.features.ai_smart_reply,
            'Memory': tierData.features.ai_memory,
            'Reprint API': tierData.features.crm_integration,
            'Display Customer Policy': tierData.features.crm_integration,
            'Notify Claim Status': tierData.features.crm_notify_claim,
            'Case System': tierData.features.crm_case_system,
            'LINE Integration': tierData.quota.max_line_accounts.toString(),
            'AI Workflow (unit)': tierData.quota.max_ai_workflow_units.toString(),
            'Workflow Management': tierData.features.custom_ai_workflow ? 'Customize flow (+ IT Support)' : 'Fixed Flow',
            'SLA Configuration': tierData.features.dashboard_sla_config,
            'SLA Anomaly Alert': tierData.features.dashboard_sla_alert,
            'Broadcast': tierData.features.broadcasting,
            'Message/Minutes': tierData.quota.max_messages_per_min.toString(),
            'Storage': `${tierData.quota.max_storage_gb} GB`,
            'IT Support': tierData.id === 'premium' ? 'Working hour' : '24/7'
        };

        return {
            id: tierData.id,
            name: tierData.name,
            price: tierData.price,
            period: tierData.period,
            description: tierData.description,
            icon: tierData.icon,
            popular: tierData.popular,
            color: tierData.color,
            features: featureMap,
            highlights: tierData.highlights
        };
    }

    /**
     * Create feature categories from backend data
     */
    static createFeatureCategories(): FeatureCategory[] {
        return [
            {
                id: "ticket-management",
                name: "Ticket Management & Agent Management",
                icon: "Users",
                description: "Manage support tickets and agent workflows efficiently",
                features: ["Active User Accounts", "Transfer Algorithm", "Case Description"]
            },
            {
                id: "ai-assistant",
                name: "AI Assistant",
                icon: "Bot",
                description: "Intelligent automation and response capabilities",
                features: ["Quick Reply", "Smart Reply", "Memory"]
            },
            {
                id: "crm-integration",
                name: "CRM Integration",
                icon: "Database",
                description: "Customer relationship management and data integration",
                features: ["Reprint API", "Display Customer Policy", "Notify Claim Status", "Case System"]
            },
            {
                id: "chatbot-management",
                name: "Chatbot Logic Management & Integration",
                icon: "MessageSquare",
                description: "Advanced chatbot workflows and platform integrations",
                features: ["LINE Integration", "AI Workflow (unit)", "Workflow Management"]
            },
            {
                id: "dashboard",
                name: "Dashboard",
                icon: "BarChart3",
                description: "Analytics, monitoring, and performance tracking",
                features: ["SLA Configuration", "SLA Anomaly Alert"]
            },
            {
                id: "social-application",
                name: "Social Application",
                icon: "MessageSquare",
                description: "Social media and broadcast messaging capabilities",
                features: ["Broadcast"]
            },
            {
                id: "service-capability",
                name: "Service Capability",
                icon: "Clock",
                description: "Core service limits and support options",
                features: ["Message/Minutes", "Storage", "IT Support"]
            }
        ];
    }

    /**
     * Transform complete backend data to frontend subscription data format
     */
    static transformToSubscriptionData(
        quotaData: SubscriptionQuotaData,
        infoData: SubscriptionInfoData | null,
        tiersData: SubscriptionTierData[]
    ): SubscriptionData {
        const currentSubscription = this.transformToCurrentSubscription(quotaData, infoData || undefined);
        const usageStats = this.transformToUsageStats(quotaData);
        const tiers = tiersData.map(tier => this.transformToSubscriptionTier(tier));
        const featureCategories = this.createFeatureCategories();

        return {
            currentSubscription,
            tiers,
            featureCategories,
            quotaData,
            styles: {
                colors: {
                    primary: "#2563eb",
                    secondary: "#64748b",
                    accent: "#7c3aed",
                    success: "#16a34a",
                    warning: "#ea580c",
                    error: "#dc2626",
                    background: "#ffffff",
                    foreground: "#0f172a",
                    muted: "#64748b",
                    card: "#f8fafc",
                    border: "#e2e8f0"
                },
                typography: {
                    fontFamily: {
                        sans: ["Inter", "system-ui", "sans-serif"],
                        mono: ["JetBrains Mono", "monospace"]
                    },
                    fontSize: {
                        xs: "0.75rem",
                        sm: "0.875rem",
                        base: "1rem",
                        lg: "1.125rem",
                        xl: "1.25rem",
                        "2xl": "1.5rem",
                        "3xl": "1.875rem"
                    }
                },
                spacing: {
                    xs: "0.5rem",
                    sm: "1rem",
                    md: "1.5rem",
                    lg: "2rem",
                    xl: "3rem"
                },
                borderRadius: {
                    sm: "0.375rem",
                    md: "0.5rem",
                    lg: "0.75rem",
                    xl: "1rem"
                }
            },
            ui: {
                components: {
                    card: {
                        className: "rounded-lg border bg-card text-card-foreground shadow-sm",
                        variants: {
                            default: "border-border",
                            highlighted: "ring-2 ring-primary",
                            current: "bg-primary/5 border-primary/20"
                        }
                    },
                    button: {
                        className: "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
                        variants: {
                            primary: "bg-primary text-primary-foreground hover:bg-primary/90",
                            secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
                            outline: "border border-input hover:bg-accent hover:text-accent-foreground"
                        }
                    },
                    badge: {
                        className: "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold",
                        variants: {
                            default: "bg-primary text-primary-foreground",
                            secondary: "bg-secondary text-secondary-foreground",
                            success: "bg-green-100 text-green-800",
                            warning: "bg-yellow-100 text-yellow-800"
                        }
                    }
                }
            },
            mockData: {
                activationCodes: [infoData?.subscription_key || "N/A"],
                usageStats,
                notifications: [
                    {
                        type: "info",
                        message: `Your ${quotaData.tier_name} subscription expires on ${new Date(quotaData.expires_at).toLocaleDateString()}`,
                        timestamp: new Date().toISOString()
                    }
                ]
            }
        };
    }
}
