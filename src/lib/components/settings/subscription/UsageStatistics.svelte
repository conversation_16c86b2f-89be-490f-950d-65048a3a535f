<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { SubscriptionQuotaData } from '$lib/api/types/subscription-api';
	import {
		UsersSolid,
		ServerSolid,
		CogSolid,
		CloseCircleSolid,
		CheckCircleSolid
	} from 'flowbite-svelte-icons';

	export let quotaData: SubscriptionQuotaData;
	export let detailed: boolean = false;

	// Calculate usage percentages and status
	function calculateUsage(current: number | string, limit: string | number | boolean): {
		percentage: number;
		status: 'good' | 'warning' | 'critical';
		display: string;
	} {
		if (typeof limit === 'boolean' || limit === 'Unlimited') {
			return {
				percentage: 0,
				status: 'good',
				display: limit === 'Unlimited' ? 'Unlimited' : limit ? 'Available' : 'Not Available'
			};
		}

		const currentNum = typeof current === 'string' ? parseFloat(current) : current;
		const limitNum = typeof limit === 'string' ? parseFloat(limit) : limit;

		if (isNaN(currentNum) || isNaN(limitNum) || limitNum === 0) {
			return { percentage: 0, status: 'good', display: 'N/A' };
		}

		const percentage = (currentNum / limitNum) * 100;
		let status: 'good' | 'warning' | 'critical' = 'good';

		if (percentage >= 90) status = 'critical';
		else if (percentage >= 75) status = 'warning';

		return {
			percentage: Math.min(percentage, 100),
			status,
			display: `${currentNum} / ${limitNum}`
		};
	}

	// Usage items configuration based on real API data
	$: usageItems = [
		{
			key: 'users',
			label: t('subscription_item_active_users'),
			icon: UsersSolid,
			current: quotaData.quota.current_active_users,
			limit: quotaData.quota.max_active_users,
			color: 'blue'
		},
		{
			key: 'line_accounts',
			label: t('subscription_item_line_accounts'),
			icon: CogSolid,
			current: 0, // API doesn't provide current LINE account usage
			limit: quotaData.quota.max_line_accounts,
			color: 'orange'
		},
		{
			key: 'ai_workflows',
			label: t('subscription_item_ai_workflow_units'),
			icon: CogSolid,
			current: 0, // API doesn't provide current AI workflow usage
			limit: quotaData.quota.max_ai_workflow_units,
			color: 'indigo'
		},
		{
			key: 'messages',
			label: t('subscription_item_messages_per_minute'),
			icon: CogSolid,
			current: 0, // API doesn't provide current message usage
			limit: quotaData.quota.max_messages_per_min,
			color: 'purple'
		},
		{
			key: 'storage',
			label: t('subscription_item_storage_limit'),
			icon: ServerSolid,
			current: 0, // API doesn't provide current storage usage
			limit: quotaData.quota.max_storage_gb,
			color: 'green',
			isStorage: true,
			unit: 'GB'
		}
	];

	function getStatusColor(status: string): string {
		switch (status) {
			case 'critical': return 'text-red-600 bg-red-100';
			case 'warning': return 'text-yellow-600 bg-yellow-100';
			default: return 'text-green-600 bg-green-100';
		}
	}

	function getProgressBarColor(status: string): string {
		switch (status) {
			case 'critical': return 'bg-red-500';
			case 'warning': return 'bg-yellow-500';
			default: return 'bg-green-500';
		}
	}

	function parseStorageValue(value: string): number {
		const match = value.match(/(\d+(?:\.\d+)?)\s*(GB|TB|MB)/i);
		if (!match) return 0;
		
		const num = parseFloat(match[1]);
		const unit = match[2].toUpperCase();
		
		switch (unit) {
			case 'TB': return num * 1024;
			case 'GB': return num;
			case 'MB': return num / 1024;
			default: return num;
		}
	}

	function calculateStorageUsage(current: string, limit: string | number | boolean) {
		if (typeof limit === 'boolean' || limit === 'Unlimited') {
			return {
				percentage: 0,
				status: 'good' as const,
				display: limit === 'Unlimited' ? 'Unlimited' : 'N/A'
			};
		}

		const currentGB = parseStorageValue(current);
		const limitGB = typeof limit === 'string' ? parseStorageValue(limit) : limit;

		if (limitGB === 0) {
			return { percentage: 0, status: 'good' as const, display: 'N/A' };
		}

		const percentage = (currentGB / limitGB) * 100;
		let status: 'good' | 'warning' | 'critical' = 'good';

		if (percentage >= 90) status = 'critical';
		else if (percentage >= 75) status = 'warning';

		return {
			percentage: Math.min(percentage, 100),
			status,
			display: `${current} / ${limit}`
		};
	}

	// Format feature names for display
	function formatFeatureName(featureKey: string): string {
		const featureNames: Record<string, string> = {
			custom_transfer_algo: 'Custom Transfer Algorithm',
			custom_case_desc: 'Custom Case Description',
			custom_ai_workflow: 'Custom AI Workflow',
			ai_quick_reply: 'AI Quick Reply',
			ai_smart_reply: 'AI Smart Reply',
			ai_memory: 'AI Memory',
			crm_integration: 'CRM Integration',
			crm_notify_claim: 'CRM Notify Claim',
			crm_case_system: 'CRM Case System',
			dashboard_sla_config: 'Dashboard SLA Config',
			dashboard_sla_alert: 'Dashboard SLA Alert',
			broadcasting: 'Broadcasting'
		};
		return featureNames[featureKey] || featureKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
	}
</script>

<div class="space-y-6">
	<div class="bg-white rounded-lg shadow-md border p-6">
		<div class="flex items-center justify-between mb-6">
			<div>
				<h2 class="text-xl font-semibold text-gray-900">
					{t('subscription_usage_stats')}
				</h2>
				<!-- <p class="text-sm text-gray-600 mt-1">
					{t('current_usage_vs_limits') || 'Current usage and quota limits'}
				</p> -->
			</div>
			<!-- <div class="text-right">
				<p class="text-sm text-gray-500">{t('subscription_tier')}</p>
				<p class="font-semibold text-gray-900">{quotaData.tier_name}</p>
				<p class="text-xs text-gray-500">{quotaData.organization_name}</p>
			</div> -->
		</div>

		<!-- Usage Cards Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
			{#each usageItems as item}
				{@const usage = item.isStorage ? 
					calculateStorageUsage(item.current.toString(), item.limit) : 
					calculateUsage(item.current, item.limit)}
				
				<div class="bg-gray-50 rounded-lg p-4 border">
					<div class="flex items-center justify-between mb-3">
						<div class="flex items-center space-x-2">
							<!-- <div class="p-2 bg-{item.color}-100 rounded-lg">
								<svelte:component this={item.icon} class="h-5 w-5 text-{item.color}-600" />
							</div> -->
							<h3 class="font-medium text-gray-900 text-sm">{item.label}</h3>
						</div>
						
						<!-- {#if usage.status !== 'good'}
							<div class="flex items-center space-x-1">
								{#if usage.status === 'critical'}
									<ExclamationCircleSolid class="h-4 w-4 text-red-500" />
								{:else}
									<ExclamationCircleSolid class="h-4 w-4 text-yellow-500" />
								{/if}
							</div>
						{:else}
							<CheckCircleSolid class="h-4 w-4 text-green-500" />
						{/if} -->
					</div>

					<!-- Usage Display -->
					<div class="space-y-2">
						<div class="flex items-center justify-between">
							<span class="text-lg font-semibold text-gray-900">
								{item.current.toLocaleString()}
								{#if item.unit}
									{item.unit}
								{/if}
							</span>
							<span class="text-xs px-2 py-1 rounded-full {getStatusColor(usage.status)}">
								{typeof item.limit === 'string' && item.limit === 'unlimited' ? 
									t('subscription_item_unlimited') :
								 	usage.percentage.toFixed(0) + '%'
								}
							</span>
						</div>

						{#if typeof item.limit !== 'string' || item.limit !== 'unlimited'}
							<!-- {#if usage.percentage > 0} -->
								<!-- Progress Bar -->
								<div class="w-full bg-gray-200 rounded-full h-2">
									<div
										class="h-2 rounded-full transition-all duration-300 {getProgressBarColor(usage.status)}"
										style="width: {usage.percentage}%"
									></div>
								</div>
							<!-- {/if} -->

							<p class="text-xs text-gray-600">
								{item.current} / {item.limit}
								{#if item.unit}
									{item.unit}
								{/if}
								<!-- {#if usage.status === 'critical'}
									- {t('subscription_item_limit_exceeded')}
								{:else if usage.status === 'warning'}
									- {t('subscription_item_approaching_limit')}
								{/if} -->
							</p>
						{:else}
							<p class="text-xs text-gray-600">Unlimited</p>
						{/if}
					</div>
				</div>
			{/each}
		</div>

		<!-- Available Features Section -->
		<div class="mt-8 pt-6 border-t border-gray-200">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">
				{t('subscription_all_features')}
			</h3>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{#each Object.entries(quotaData.features) as [featureKey, isEnabled]}
					<div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
						{#if isEnabled}
							<CheckCircleSolid class="h-5 w-5 text-green-500 flex-shrink-0" />
						{:else}
							<CloseCircleSolid class="h-5 w-5 text-gray-400 flex-shrink-0" />
						{/if}
						<div>
							<p class="text-sm font-medium text-gray-900">
								{formatFeatureName(featureKey)}
							</p>
							<p class="text-xs text-gray-500">
								{isEnabled ? t('subscription_feature_available') : t('subscription_feature_not_available')}
							</p>
						</div>
					</div>
				{/each}
			</div>
		</div>
	</div>
</div>
