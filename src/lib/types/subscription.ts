export interface CurrentSubscription {
	tier: string;
	status: string;
	expiresAt: string;
	serialNumber: string;
	organizationName?: string;
	activatedOn?: string;
}

export interface SubscriptionTier {
	id: string;
	name: string;
	price: string;
	period: string;
	description: string;
	icon: string;
	popular: boolean;
	color: string;
	features: Record<string, string | boolean | number>;
	highlights: string[];
}

export interface FeatureCategory {
	id: string;
	name: string;
	icon: string;
	description: string;
	features: string[];
}

export interface UsageStats {
	currentUsers: number;
	storageUsed: string;
	messagesThisMonth: number;
	workflowsActive: number;
	maxUsers?: number;
	remainingSlots?: number;
	maxStorage?: number;
	maxMessagesPerMin?: number;
}

export interface NotificationItem {
	type: string;
	message: string;
	timestamp: string;
}

export interface SubscriptionStyles {
	colors: Record<string, string>;
	typography: {
		fontFamily: Record<string, string[]>;
		fontSize: Record<string, string>;
	};
	spacing: Record<string, string>;
	borderRadius: Record<string, string>;
}

export interface UIComponents {
	card: {
		className: string;
		variants: Record<string, string>;
	};
	button: {
		className: string;
		variants: Record<string, string>;
	};
	badge: {
		className: string;
		variants: Record<string, string>;
	};
}

export interface SubscriptionData {
	currentSubscription: CurrentSubscription;
	tiers: SubscriptionTier[];
	featureCategories: FeatureCategory[];
	quotaData: import('$lib/api/types/subscription-api').SubscriptionQuotaData;
	styles: SubscriptionStyles;
	ui: {
		components: UIComponents;
	};
	mockData: {
		activationCodes: string[];
		usageStats: UsageStats;
		notifications: NotificationItem[];
	};
}

export type SubscriptionStatus = 'active' | 'expired' | 'pending' | 'cancelled';
export type SubscriptionTab = 'overview' | 'plans' | 'features' | 'usage';
