<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { PageData } from './$types';
	import { Toast } from 'flowbite-svelte';
	import { CheckCircleSolid } from 'flowbite-svelte-icons';
	import { fly } from 'svelte/transition';

	// Import subscription components
	import CurrentSubscriptionCard from '$lib/components/settings/subscription/CurrentSubscriptionCard.svelte';
	import UsageStatistics from '$lib/components/settings/subscription/UsageStatistics.svelte';

	export let data: PageData;
	$: ({ subscriptionData, error } = data);

	// Toast state
	let toastMessage = 'Successfully updated';
	let toastStatus = false;

	// Handle subscription actions
	function handleSubscriptionAction(event: CustomEvent) {
		const { action, data: actionData } = event.detail;
		console.log('Subscription action:', action, actionData);
		
		// Show success toast
		toastMessage = `${action} completed successfully`;
		toastStatus = true;
	}
</script>

<svelte:head>
	<title>{t('subscription')}</title>
</svelte:head>

{#if toastStatus}
	<Toast
		id="subscription-toast"
		color="green"
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus
		class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
	>
		<CheckCircleSolid slot="icon" class="h-5 w-5" />
		{toastMessage}
	</Toast>
{/if}

<div class="min-h-screen rounded-lg bg-white">
	<div id="subscription-container" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900">
				{t('subscription_title')}
			</h1>
			<p class="mt-2 text-gray-600">
				{t('subscription_description')}
			</p>
		</div>

		{#if error}
			<div class="mb-6 rounded-lg bg-red-50 p-4 border border-red-200">
				<p class="text-red-800">{error}</p>
			</div>
		{:else if subscriptionData}

			<!-- Subscription Content -->
			<div id="subscription-content" class="space-y-6">
				<CurrentSubscriptionCard
					subscription={subscriptionData.currentSubscription}
					notifications={subscriptionData.mockData.notifications}
					on:action={handleSubscriptionAction}
				/>
				<UsageStatistics
					quotaData={subscriptionData.quotaData}
					detailed={true}
				/>
			</div>
		{:else}
			<div class="text-center py-12">
				<p class="text-gray-500">Loading subscription data...</p>
			</div>
		{/if}
	</div>
</div>
